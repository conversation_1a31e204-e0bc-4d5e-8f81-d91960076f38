{"name": "handbook-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "Handbook for life", "scripts": {"dev": "vite --host", "build": "vite build", "build:dev": "vite build --mode development", "build:analyze": "vite build --mode analyze", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "preview:host": "vite preview --host", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "clean": "rm -rf dist node_modules"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tiptap/extension-character-count": "^2.22.3", "@tiptap/extension-color": "^2.22.3", "@tiptap/extension-highlight": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-table": "^2.22.3", "@tiptap/extension-table-cell": "^2.22.3", "@tiptap/extension-table-header": "^2.22.3", "@tiptap/extension-table-row": "^2.22.3", "@tiptap/extension-text-align": "^2.22.3", "@tiptap/extension-text-style": "^2.22.3", "@tiptap/extension-typography": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "class-variance-authority": "^0.7.1", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.19.1", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.5"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.1", "tailwindcss": "^3.4.17", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "typescript": "^5.8.3", "vite": "^6.3.5"}}